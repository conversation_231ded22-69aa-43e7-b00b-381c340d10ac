<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useDataRefresh } from '@composables/useDataRefresh'
import WebhooksTable from './WebhooksTable.vue'

// State for webhooks data
const webhooks = ref([])
const isLoading = ref(true)

// Data refresh system
const { refreshState, updateData } = useDataRefresh()

// Load webhooks data
const loadWebhooks = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/webhooks')
    const data = await response.json()
    webhooks.value = data.webhooks || []

    // Update global data store
    updateData('webhooks', data.webhooks || [])
  } catch (error) {
    console.error('Failed to load webhooks:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.webhooks, () => {
  loadWebhooks()
})

// Handle webhook deletion
const handleDeleteWebhook = (webhookId: string, webhookName: string) => {
  if (confirm(`Are you sure you want to delete the webhook "${webhookName}"? This action cannot be undone.`)) {
    // TODO: Implement actual webhook deletion API call
    console.log('Delete webhook:', webhookId, webhookName)
    // For now, just show an alert
    alert('Webhook deletion functionality will be implemented soon.')
  }
}

onMounted(() => {
  loadWebhooks()

  // Make deleteWebhook function available globally for table onclick handlers
  ;(window as any).deleteWebhook = handleDeleteWebhook
})

onUnmounted(() => {
  // Clean up global function
  delete (window as any).deleteWebhook
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadWebhooks
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Webhooks table -->
    <WebhooksTable
      v-else
      :webhooks="webhooks"
      @refresh="loadWebhooks"
    />
  </div>
</template>
