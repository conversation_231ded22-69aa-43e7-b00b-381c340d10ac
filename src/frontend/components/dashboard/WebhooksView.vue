<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useWebhookApi } from '@composables/useApi'
import { useConfirm } from '@composables/useConfirm'
import { useToast } from '@composables/useToast'
import WebhooksTable from './WebhooksTable.vue'

// Composables
const { deleteWebhook } = useWebhookApi()
const { confirmDelete } = useConfirm()
const { success, error } = useToast()

// State for webhooks data
const webhooks = ref([])
const isLoading = ref(true)

// Data refresh system
const { refreshState, updateData } = useDataRefresh()

// Load webhooks data
const loadWebhooks = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/webhooks')
    const data = await response.json()
    webhooks.value = data.webhooks || []

    // Update global data store
    updateData('webhooks', data.webhooks || [])
  } catch (error) {
    console.error('Failed to load webhooks:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.webhooks, () => {
  loadWebhooks()
})

// Handle webhook deletion
const handleDeleteWebhook = async (webhookId: string, webhookName: string) => {
  try {
    const confirmed = await confirmDelete(webhookName, 'webhook')
    if (!confirmed) return

    await deleteWebhook(webhookId)
    success(`Webhook "${webhookName}" deleted successfully`)

    // Refresh the webhooks list
    await loadWebhooks()
  } catch (err) {
    console.error('Failed to delete webhook:', err)
    error(err instanceof Error ? err.message : 'Failed to delete webhook')
  }
}

onMounted(() => {
  loadWebhooks()
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadWebhooks
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Webhooks table -->
    <WebhooksTable
      v-else
      :webhooks="webhooks"
      @refresh="loadWebhooks"
      @delete-webhook="handleDeleteWebhook"
    />
  </div>
</template>
