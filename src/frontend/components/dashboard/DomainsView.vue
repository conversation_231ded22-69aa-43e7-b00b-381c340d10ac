<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useDataRefresh } from '@composables/useDataRefresh'
import DomainsTable from './DomainsTable.vue'

// Router
const router = useRouter()

// State for domains data
const domains = ref([])
const isLoading = ref(true)

// Data refresh system
const { refreshState, updateData } = useDataRefresh()

// Load domains data
const loadDomains = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/domains')
    const data = await response.json()
    domains.value = data.domains || []

    // Update global data store
    updateData('domains', data.domains || [])
  } catch (error) {
    console.error('Failed to load domains:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.domains, () => {
  loadDomains()
})

// Handle view logs navigation
const handleViewLogs = (domainId: string) => {
  // Navigate to logs tab and store domain selection for LogsView to pick up
  sessionStorage.setItem('logsView_selectedDomain', domainId)
  router.push('/logs')
}

// Handle domain deletion
const handleDeleteDomain = (domainId: string, domainName: string) => {
  if (confirm(`Are you sure you want to delete the domain "${domainName}"? This action cannot be undone.`)) {
    // TODO: Implement actual domain deletion API call
    console.log('Delete domain:', domainId, domainName)
    // For now, just show an alert
    alert('Domain deletion functionality will be implemented soon.')
  }
}

onMounted(() => {
  loadDomains()

  // Make functions available globally for table onclick handlers
  ;(window as any).viewLogs = handleViewLogs
  ;(window as any).deleteDomain = handleDeleteDomain
})

onUnmounted(() => {
  // Clean up global functions
  delete (window as any).viewLogs
  delete (window as any).deleteDomain
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadDomains
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Domains table -->
    <DomainsTable
      v-else
      :domains="domains"
      @refresh="loadDomains"
      @view-logs="handleViewLogs"
    />
  </div>
</template>
