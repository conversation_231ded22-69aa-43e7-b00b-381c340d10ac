<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useDomainApi } from '@composables/useApi'
import { useConfirm } from '@composables/useConfirm'
import { useToast } from '@composables/useToast'
import DomainsTable from './DomainsTable.vue'

// Router
const router = useRouter()

// Composables
const { deleteDomain } = useDomainApi()
const { confirmDelete } = useConfirm()
const { success, error } = useToast()

// State for domains data
const domains = ref([])
const isLoading = ref(true)

// Data refresh system
const { refreshState, updateData } = useDataRefresh()

// Load domains data
const loadDomains = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/domains')
    const data = await response.json()
    domains.value = data.domains || []

    // Update global data store
    updateData('domains', data.domains || [])
  } catch (error) {
    console.error('Failed to load domains:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.domains, () => {
  loadDomains()
})

// Handle view logs navigation
const handleViewLogs = (domainId: string) => {
  // Navigate to logs tab and store domain selection for LogsView to pick up
  sessionStorage.setItem('logsView_selectedDomain', domainId)
  router.push('/logs')
}

// Handle domain deletion
const handleDeleteDomain = async (domainId: string, domainName: string) => {
  try {
    const confirmed = await confirmDelete(domainName, 'domain')
    if (!confirmed) return

    await deleteDomain(domainName)
    success(`Domain "${domainName}" deleted successfully`)

    // Refresh the domains list
    await loadDomains()
  } catch (err) {
    console.error('Failed to delete domain:', err)
    error(err instanceof Error ? err.message : 'Failed to delete domain')
  }
}

onMounted(() => {
  loadDomains()
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadDomains
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Domains table -->
    <DomainsTable
      v-else
      :domains="domains"
      @refresh="loadDomains"
      @view-logs="handleViewLogs"
      @delete-domain="handleDeleteDomain"
    />
  </div>
</template>
