<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useDataRefresh } from '@composables/useDataRefresh'
import AliasesTable from './AliasesTable.vue'

// Router
const router = useRouter()

// State for aliases data
const aliases = ref([])
const isLoading = ref(true)

// Data refresh system
const { refreshState, updateData } = useDataRefresh()

// Load aliases data
const loadAliases = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/aliases')
    const data = await response.json()
    aliases.value = data.aliases || []

    // Update global data store
    updateData('aliases', data.aliases || [])
  } catch (error) {
    console.error('Failed to load aliases:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.aliases, () => {
  loadAliases()
})

// Handle view logs navigation
const handleViewLogs = (domainId: string, aliasId: string) => {
  // Navigate to logs tab and store domain and alias selection for LogsView to pick up
  sessionStorage.setItem('logsView_selectedDomain', domainId)
  sessionStorage.setItem('logsView_selectedAlias', aliasId)
  router.push('/logs')
}

// Handle alias deletion
const handleDeleteAlias = (aliasId: string, aliasName: string) => {
  if (confirm(`Are you sure you want to delete the alias "${aliasName}"? This action cannot be undone.`)) {
    // TODO: Implement actual alias deletion API call
    console.log('Delete alias:', aliasId, aliasName)
    // For now, just show an alert
    alert('Alias deletion functionality will be implemented soon.')
  }
}

onMounted(() => {
  loadAliases()

  // Make functions available globally for table onclick handlers
  ;(window as any).viewAliasLogs = handleViewLogs
  ;(window as any).deleteAlias = handleDeleteAlias
})

onUnmounted(() => {
  // Clean up global functions
  delete (window as any).viewAliasLogs
  delete (window as any).deleteAlias
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadAliases
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Aliases table -->
    <AliasesTable
      v-else
      :aliases="aliases"
      @refresh="loadAliases"
      @view-logs="handleViewLogs"
    />
  </div>
</template>
