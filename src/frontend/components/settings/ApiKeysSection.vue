<template>
  <div class="card bg-base-100">
    <div class="card-body pt-0">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="card-title">API keys</h2>
          <p class="mt-1 text-base-content/70">
            Manage your API keys for programmatic access to your domains, aliases, and webhooks.
          </p>
        </div>
        <button
          @click="showCreateApiKeyModal = true"
          class="btn btn-primary"
        >
          Generate key
        </button>
      </div>

      <!-- API Keys List -->
      <div v-if="apiKeys.length > 0" class="bg-base-200 rounded-lg p-6">
        <div class="space-y-3">
          <div
            v-for="apiKey in apiKeys"
            :key="apiKey.id"
            class="flex items-center justify-between p-4 bg-base-100 rounded-lg"
          >
            <div class="flex-1">
              <h3 class="font-medium">{{ apiKey.name }}</h3>
              <p class="text-sm text-base-content/70 font-mono">{{ apiKey.keyPrefix }}</p>
              <p class="text-xs text-base-content/50">
                Created {{ formatDate(apiKey.createdAt) }}
                <span v-if="apiKey.lastUsedAt">
                  • Last used {{ formatDate(apiKey.lastUsedAt) }}
                </span>
                <span v-else>
                  • Never used
                </span>
              </p>
            </div>
            <button
              @click="revokeApiKey(apiKey)"
              class="btn btn-outline btn-error btn-sm"
            >
              Revoke
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="bg-base-200 rounded-lg p-6">
        <div class="py-12 text-center">
          <svg class="w-12 h-12 mx-auto text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10zM12 11v2m0 4h.01" />
          </svg>

          <h3 class="mt-4 text-lg font-medium">No API keys</h3>
          <p class="mt-2 text-base-content/70">Get started by creating your first API key.</p>
          <button
            @click="showCreateApiKeyModal = true"
            class="btn btn-primary mt-4"
          >
            Generate API key
          </button>
        </div>
      </div>
    </div>

    <!-- Create API Key Modal -->
    <div v-if="showCreateApiKeyModal" class="modal modal-open">
      <div class="modal-box">
        <h3 class="text-lg font-bold mb-4">Generate new API key</h3>

        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">Key name</span>
          </label>
          <input
            v-model="newApiKeyName"
            type="text"
            placeholder="e.g., Production API, Development Key"
            class="input input-bordered w-full"
          >
          <label class="label">
            <span class="label-text-alt text-xs">Choose a descriptive name to identify this key.</span>
          </label>
        </div>

        <div class="modal-action">
          <button
            @click="showCreateApiKeyModal = false"
            class="btn btn-ghost"
          >
            Cancel
          </button>
          <button
            @click="generateApiKey"
            :disabled="!newApiKeyName.trim() || generatingKey"
            class="btn btn-primary"
          >
            {{ generatingKey ? 'Generating...' : 'Generate key' }}
          </button>
        </div>
      </div>
    </div>

    <!-- API Key Generated Modal -->
    <div v-if="showApiKeyModal" class="modal modal-open">
      <div class="modal-box max-w-lg">
        <div class="flex items-center mb-4">
          <svg class="w-6 h-6 mr-2 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="text-lg font-bold">API key generated</h3>
        </div>

        <div class="alert alert-warning mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" /></svg>
          <div>
            <h4 class="font-bold">Important: save this key now!</h4>
            <div class="text-sm">This is the only time you'll see the full API key. Store it securely.</div>
          </div>
        </div>

        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">Your API key</span>
          </label>
          <div class="join w-full">
            <input
              :value="generatedApiKey"
              readonly
              class="input input-bordered join-item flex-1 font-mono text-sm bg-neutral text-neutral-content cursor-not-allowed"
            >
            <button
              @click="copyApiKey"
              class="btn btn-primary join-item"
            >
              {{ copied ? 'Copied!' : 'Copy' }}
            </button>
          </div>
        </div>

        <div class="modal-action">
          <button
            @click="closeApiKeyModal"
            class="btn btn-primary"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Types
interface ApiKey {
  id: string
  name: string
  keyPrefix: string
  createdAt: string
  lastUsedAt: string | null
}

// State
const apiKeys = ref<ApiKey[]>([])
const showCreateApiKeyModal = ref(false)
const showApiKeyModal = ref(false)
const newApiKeyName = ref('')
const generatingKey = ref(false)
const generatedApiKey = ref('')
const copied = ref(false)

// Methods
const loadApiKeys = async () => {
  try {
    const response = await fetch('/api/api-keys', {
      credentials: 'include'
    })

    if (response.ok) {
      const data = await response.json()
      apiKeys.value = data.apiKeys || []
    } else {
      console.error('Failed to load API keys')
    }
  } catch (error) {
    console.error('Error loading API keys:', error)
  }
}

const generateApiKey = async () => {
  if (!newApiKeyName.value.trim()) return

  generatingKey.value = true
  try {
    const response = await fetch('/api/api-keys', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        name: newApiKeyName.value.trim()
      })
    })

    if (response.ok) {
      const data = await response.json()
      generatedApiKey.value = data.apiKey.key
      showCreateApiKeyModal.value = false
      showApiKeyModal.value = true
      newApiKeyName.value = ''

      // Reload API keys list
      await loadApiKeys()
    } else {
      const error = await response.json()
      alert(`Failed to generate API key: ${error.message}`)
    }
  } catch (error) {
    console.error('Error generating API key:', error)
    alert('Failed to generate API key. Please try again.')
  } finally {
    generatingKey.value = false
  }
}

const revokeApiKey = async (apiKey: ApiKey) => {
  if (!confirm(`Are you sure you want to revoke "${apiKey.name}"? This action cannot be undone.`)) {
    return
  }

  try {
    const response = await fetch(`/api/api-keys/${apiKey.id}`, {
      method: 'DELETE',
      credentials: 'include'
    })

    if (response.ok) {
      // Remove from local list
      apiKeys.value = apiKeys.value.filter(k => k.id !== apiKey.id)
    } else {
      const error = await response.json()
      alert(`Failed to revoke API key: ${error.message}`)
    }
  } catch (error) {
    console.error('Error revoking API key:', error)
    alert('Failed to revoke API key. Please try again.')
  }
}

const copyApiKey = async () => {
  try {
    await navigator.clipboard.writeText(generatedApiKey.value)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy API key:', error)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = generatedApiKey.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  }
}

const closeApiKeyModal = () => {
  showApiKeyModal.value = false
  generatedApiKey.value = ''
  copied.value = false
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

onMounted(() => {
  loadApiKeys()
})
</script>
